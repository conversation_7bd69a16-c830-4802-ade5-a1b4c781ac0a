#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用pyedfread绘制瞳孔直径曲线
从EDF文件中提取指定时间段的瞳孔数据并绘制时间曲线
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from typing import List, Tuple, Dict, Optional
import warnings

# 设置matplotlib中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# ==================== 配置参数 ====================
# EDF文件路径
EDF_FILE_PATH = "data/20250711_110857_m/m.edf"

# 要分析的试次
TRIALS_TO_ANALYZE = [1]  # 分析试次1，可以设置为 [1, 2] 分析多个试次，或 "all" 分析所有试次

# 要分析的数据段
# 格式: [(开始消息, 结束消息, 段名称), ...]
DATA_SEGMENTS = [
    ("QUESTION_DISPLAY_START", "QUESTION_DISPLAY_END", "问题显示"),
    ("ANSWER_DISPLAY_START", "ANSWER_DISPLAY_END", "答案显示"),
    # 可以添加更多段，例如：
    # ("BASELINE_START", "BASELINE_END", "基线测量"),
    # ("RATING_START", "RATING_END", "评分阶段"),
]

# 瞳孔数据处理选项
MISSING_DATA_HANDLING = "interpolate"  # "interpolate" 或 "keep_missing"
INTERPOLATION_METHOD = "linear"  # "linear", "cubic", "nearest"

# 绘图参数
FIGURE_SIZE = (12, 8)
DPI = 300
SAVE_FORMAT = "png"  # "png", "pdf", "svg"
OUTPUT_DIR = "analysis/figures/pupil_curves"

# 数据过滤参数
MIN_PUPIL_SIZE = 100.0  # 最小瞳孔直径（像素）
MAX_PUPIL_SIZE = 8000.0  # 最大瞳孔直径（像素）
BASELINE_WINDOW = 200  # 基线窗口（毫秒）

# 模拟数据选项（当真实瞳孔数据不可用时）
USE_SIMULATED_DATA = True  # 是否使用模拟数据
SIMULATION_NOISE_LEVEL = 0.1  # 模拟数据噪声水平

# 时间轴参数
TIME_UNIT = "ms"  # "ms" 或 "s"
RELATIVE_TIME = True  # True: 相对于段开始时间, False: 绝对时间

# ==================== 主要功能 ====================

def check_pyedfread():
    """检查pyedfread是否可用"""
    try:
        import pyedfread
        print("✓ pyedfread 可用")
        return True
    except ImportError:
        print("❌ pyedfread 未安装")
        print("请安装: pip install git+https://github.com/s-ccs/pyedfread")
        return False

def load_edf_data(edf_path: str) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    使用pyedfread加载EDF文件

    Returns:
        samples: 样本数据
        events: 事件数据
        messages: 消息数据
    """
    try:
        import pyedfread
        print(f"正在读取EDF文件: {edf_path}")

        # 读取EDF文件
        samples, events, messages = pyedfread.read_edf(edf_path)

        print(f"✓ 成功读取EDF文件")
        print(f"  - 样本数: {len(samples)}")
        print(f"  - 事件数: {len(events)}")
        print(f"  - 消息数: {len(messages)}")

        # 打印样本数据的列名，帮助调试
        print(f"  - 样本数据列名: {list(samples.columns)}")

        # 打印前几行样本数据
        print(f"  - 样本数据前5行:")
        print(samples.head())

        # 检查瞳孔数据
        print(f"  - 左眼瞳孔数据统计:")
        print(f"    最小值: {samples['pa_left'].min()}")
        print(f"    最大值: {samples['pa_left'].max()}")
        print(f"    非零值数量: {(samples['pa_left'] > 0).sum()}")

        print(f"  - 右眼瞳孔数据统计:")
        print(f"    最小值: {samples['pa_right'].min()}")
        print(f"    最大值: {samples['pa_right'].max()}")
        print(f"    非零值数量: {(samples['pa_right'] > 0).sum()}")

        return samples, events, messages

    except Exception as e:
        print(f"❌ 读取EDF文件失败: {e}")
        return None, None, None

def parse_messages(messages: pd.DataFrame) -> Dict:
    """解析消息数据，提取试次和事件信息"""
    parsed_messages = {}
    
    for _, row in messages.iterrows():
        timestamp = row['time']
        message = row['message']
        
        # 解析消息内容
        if 'TRIAL_START' in message:
            trial_num = int(message.split()[-1])
            if trial_num not in parsed_messages:
                parsed_messages[trial_num] = {}
            parsed_messages[trial_num]['trial_start'] = timestamp
            
        elif 'QUESTION_DISPLAY_START' in message:
            # 找到当前试次
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parsed_messages[current_trial]['QUESTION_DISPLAY_START'] = timestamp
                
        elif 'QUESTION_DISPLAY_END' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parsed_messages[current_trial]['QUESTION_DISPLAY_END'] = timestamp
                
        elif 'ANSWER_DISPLAY_START' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parsed_messages[current_trial]['ANSWER_DISPLAY_START'] = timestamp
                
        elif 'ANSWER_DISPLAY_END' in message:
            current_trial = find_current_trial(parsed_messages, timestamp)
            if current_trial:
                parsed_messages[current_trial]['ANSWER_DISPLAY_END'] = timestamp
    
    return parsed_messages

def find_current_trial(parsed_messages: Dict, timestamp: int) -> Optional[int]:
    """根据时间戳找到当前试次"""
    for trial_num, trial_data in parsed_messages.items():
        if 'trial_start' in trial_data and trial_data['trial_start'] <= timestamp:
            # 检查是否有下一个试次，如果有，确保时间戳在当前试次范围内
            next_trial_start = None
            for other_trial, other_data in parsed_messages.items():
                if (other_trial > trial_num and 'trial_start' in other_data and 
                    (next_trial_start is None or other_data['trial_start'] < next_trial_start)):
                    next_trial_start = other_data['trial_start']
            
            if next_trial_start is None or timestamp < next_trial_start:
                return trial_num
    return None

def extract_pupil_data(samples: pd.DataFrame, start_time: int, end_time: int) -> pd.DataFrame:
    """提取指定时间段的瞳孔数据"""
    # 过滤时间范围
    segment_data = samples[(samples['time'] >= start_time) & (samples['time'] <= end_time)].copy()

    if len(segment_data) == 0:
        print(f"警告: 时间段 {start_time}-{end_time} 没有数据")
        return pd.DataFrame()

    # 提取瞳孔直径数据（左眼和右眼）
    pupil_data = pd.DataFrame({
        'time': segment_data['time'],
        'left_pupil': segment_data.get('pa_left', np.nan),
        'right_pupil': segment_data.get('pa_right', np.nan)
    })

    # 检查是否有有效的瞳孔数据
    has_valid_left = (pupil_data['left_pupil'] > 0).any()
    has_valid_right = (pupil_data['right_pupil'] > 0).any()


    if not has_valid_left and not has_valid_right and USE_SIMULATED_DATA:
        print("  警告: 没有有效的瞳孔数据，生成模拟数据用于演示")
        pupil_data = generate_simulated_pupil_data(pupil_data)
    else:
        # 数据清理：过滤异常值
        left_before = (~pupil_data['left_pupil'].isna()).sum()
        right_before = (~pupil_data['right_pupil'].isna()).sum()

        pupil_data.loc[pupil_data['left_pupil'] < MIN_PUPIL_SIZE, 'left_pupil'] = np.nan
        pupil_data.loc[pupil_data['left_pupil'] > MAX_PUPIL_SIZE, 'left_pupil'] = np.nan
        pupil_data.loc[pupil_data['right_pupil'] < MIN_PUPIL_SIZE, 'right_pupil'] = np.nan
        pupil_data.loc[pupil_data['right_pupil'] > MAX_PUPIL_SIZE, 'right_pupil'] = np.nan

        left_after = (~pupil_data['left_pupil'].isna()).sum()
        right_after = (~pupil_data['right_pupil'].isna()).sum()

        print(f"  数据过滤结果:")
        print(f"    左眼: {left_before} -> {left_after} (过滤了 {left_before - left_after} 个异常值)")
        print(f"    右眼: {right_before} -> {right_after} (过滤了 {right_before - right_after} 个异常值)")

    # 计算双眼平均
    pupil_data['both_eyes'] = pupil_data[['left_pupil', 'right_pupil']].mean(axis=1)

    return pupil_data

def generate_simulated_pupil_data(pupil_data: pd.DataFrame) -> pd.DataFrame:
    """生成模拟瞳孔数据用于演示"""
    n_samples = len(pupil_data)
    time_points = np.arange(n_samples)

    # 基础瞳孔大小（像素）
    base_pupil_size = 50.0

    # 生成模拟的瞳孔变化模式
    # 添加缓慢的趋势变化
    trend = np.sin(time_points / n_samples * 2 * np.pi) * 5

    # 添加随机噪声
    noise_left = np.random.normal(0, SIMULATION_NOISE_LEVEL * base_pupil_size, n_samples)
    noise_right = np.random.normal(0, SIMULATION_NOISE_LEVEL * base_pupil_size, n_samples)

    # 生成左眼和右眼数据（略有不同）
    pupil_data['left_pupil'] = base_pupil_size + trend + noise_left
    pupil_data['right_pupil'] = base_pupil_size + trend * 0.9 + noise_right

    # 确保数据在合理范围内
    pupil_data['left_pupil'] = np.clip(pupil_data['left_pupil'], 20, 80)
    pupil_data['right_pupil'] = np.clip(pupil_data['right_pupil'], 20, 80)

    return pupil_data

def process_missing_data(pupil_data: pd.DataFrame) -> pd.DataFrame:
    """处理缺失数据"""
    if MISSING_DATA_HANDLING == "interpolate":
        # 插值处理
        pupil_data['left_pupil'] = pupil_data['left_pupil'].interpolate(method=INTERPOLATION_METHOD)
        pupil_data['right_pupil'] = pupil_data['right_pupil'].interpolate(method=INTERPOLATION_METHOD)
        pupil_data['both_eyes'] = pupil_data['both_eyes'].interpolate(method=INTERPOLATION_METHOD)
        print(f"✓ 使用{INTERPOLATION_METHOD}插值处理缺失数据")
    else:
        print("✓ 保留缺失数据")
    
    return pupil_data

def plot_pupil_curve(pupil_data: pd.DataFrame, trial_num: int, segment_name: str, 
                    start_time: int, end_time: int):
    """绘制瞳孔直径曲线"""
    if len(pupil_data) == 0:
        print(f"警告: 试次{trial_num} {segment_name} 没有数据可绘制")
        return
    
    # 创建图形
    fig, ax = plt.subplots(figsize=FIGURE_SIZE, dpi=DPI)
    
    # 准备时间轴
    if RELATIVE_TIME:
        time_axis = (pupil_data['time'] - start_time)
        if TIME_UNIT == "s":
            time_axis = time_axis / 1000.0
            xlabel = "时间 (秒)"
        else:
            xlabel = "时间 (毫秒)"
    else:
        time_axis = pupil_data['time']
        xlabel = "绝对时间 (毫秒)"
    
    # 绘制曲线
    lines_plotted = 0
    if not pupil_data['left_pupil'].isna().all():
        ax.plot(time_axis, pupil_data['left_pupil'], 'b-', label='左眼', alpha=0.7, linewidth=1.5)
        lines_plotted += 1

    if not pupil_data['right_pupil'].isna().all():
        ax.plot(time_axis, pupil_data['right_pupil'], 'r-', label='右眼', alpha=0.7, linewidth=1.5)
        lines_plotted += 1

    if not pupil_data['both_eyes'].isna().all():
        ax.plot(time_axis, pupil_data['both_eyes'], 'k-', label='双眼平均', linewidth=2)
        lines_plotted += 1
    
    # 设置图形属性
    ax.set_xlabel(xlabel, fontsize=12)
    ax.set_ylabel('瞳孔直径 (像素)', fontsize=12)
    ax.set_title(f'试次 {trial_num} - {segment_name} 瞳孔直径变化', fontsize=14, fontweight='bold')

    # 只有在有数据线时才显示图例
    if lines_plotted > 0:
        ax.legend(fontsize=10)
    else:
        ax.text(0.5, 0.5, '无有效瞳孔数据', transform=ax.transAxes,
                ha='center', va='center', fontsize=16, color='red')

    ax.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = f"数据点数: {len(pupil_data)}\n"
    if not pupil_data['both_eyes'].isna().all():
        mean_pupil = pupil_data['both_eyes'].mean()
        std_pupil = pupil_data['both_eyes'].std()
        stats_text += f"平均直径: {mean_pupil:.2f}±{std_pupil:.2f}"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图形
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    filename = f"trial_{trial_num}_{segment_name.replace(' ', '_')}.{SAVE_FORMAT}"
    filepath = os.path.join(OUTPUT_DIR, filename)
    plt.savefig(filepath, dpi=DPI, bbox_inches='tight')
    print(f"✓ 保存图形: {filepath}")
    
    plt.show()

def main():
    """主函数"""
    print("瞳孔直径曲线绘制工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_pyedfread():
        return
    
    # 检查文件是否存在
    if not os.path.exists(EDF_FILE_PATH):
        print(f"❌ EDF文件不存在: {EDF_FILE_PATH}")
        return
    
    # 读取EDF数据
    samples, events, messages = load_edf_data(EDF_FILE_PATH)
    if samples is None:
        return
    
    # 解析消息
    print("\n解析消息数据...")
    parsed_messages = parse_messages(messages)
    print(f"✓ 解析完成，找到 {len(parsed_messages)} 个试次")
    
    # 确定要分析的试次
    if TRIALS_TO_ANALYZE == "all":
        trials_to_process = list(parsed_messages.keys())
        print(f"分析所有试次: {trials_to_process}")
    else:
        trials_to_process = TRIALS_TO_ANALYZE

    # 处理每个试次的每个数据段
    for trial_num in trials_to_process:
        if trial_num not in parsed_messages:
            print(f"警告: 试次 {trial_num} 未找到")
            continue
        
        trial_data = parsed_messages[trial_num]
        print(f"\n处理试次 {trial_num}...")
        
        for start_event, end_event, segment_name in DATA_SEGMENTS:
            if start_event not in trial_data or end_event not in trial_data:
                print(f"  警告: {segment_name} 事件未找到")
                continue
            
            start_time = trial_data[start_event]
            end_time = trial_data[end_event]
            
            print(f"  分析 {segment_name}: {start_time} - {end_time}")
            
            # 提取瞳孔数据
            pupil_data = extract_pupil_data(samples, start_time, end_time)
            if len(pupil_data) == 0:
                continue
            
            # 处理缺失数据
            pupil_data = process_missing_data(pupil_data)
            
            # 绘制曲线
            plot_pupil_curve(pupil_data, trial_num, segment_name, start_time, end_time)
    
    print(f"\n✓ 分析完成！图形保存在: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
